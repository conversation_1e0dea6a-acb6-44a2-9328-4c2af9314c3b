<?xml version="1.0" encoding="utf-8"?>
<odoo>
	<data>
		<record id="view_partner_inherit_followup_form" model="ir.ui.view">
			<field name="name">res.partner</field>
			<field name="model">res.partner</field>
			<field name="inherit_id" ref="base.view_partner_form" />
			<field name="arch" type="xml">
				<field name='category_id' position='after'>
					<field name='opt_statement'/>
					<field name="company_id" options="{'no_create': True}"/>
					<field name="statments" invisible="1"/>
					<field name="is_set_statments" invisible="1"/>
				</field>
<!--				<page name="accounting" position='after'>-->
				<xpath expr="//page[@name='accounting']" position="after">

					<page string="Customer Statements" groups="account.group_account_invoice"
						name="followup_tab" context="{'res_partner_search_mode': 'customer'}" >
						<separator string="Customer Statements Filter By Date"/>
						<!-- 美化的天数过滤器方块 -->
						<style>
							.aging-button:hover {
								background: rgba(255,255,255,0.8) !important;
								transform: translateY(-2px);
								box-shadow: 0 8px 16px rgba(0,0,0,0.2) !important;
							}
						</style>
						<div style="margin: 20px 0;">
							<div style="display: flex; justify-content: center; gap: 20px; padding: 25px; background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); border-radius: 15px; box-shadow: 0 6px 20px rgba(0,0,0,0.1); border: 1px solid #dee2e6;">
								<div style="text-align: center;">
									<button name="set_date_filter_0_30" type="object"
										class="btn btn-outline-info aging-button"
										style="width: 120px; height: 120px; font-size: 11px; font-weight: bold; border-radius: 15px; border-width: 2px; box-shadow: 0 6px 12px rgba(23,162,184,0.25); transition: all 0.3s; background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%); position: relative; overflow: hidden; display: flex; flex-direction: column; justify-content: center; align-items: center;">
										<div style="font-size: 9px; color: #6c757d; margin-bottom: 2px; text-transform: uppercase; letter-spacing: 0.5px;">Current</div>
										<div style="font-size: 13px; font-weight: bold; color: #17a2b8; margin-bottom: 4px;">0-30 Days</div>
										<div style="font-size: 12px; font-weight: bold; color: #17a2b8; background: rgba(23,162,184,0.1); padding: 2px 6px; border-radius: 4px;">
											<field name="first_thirty_day_filter" readonly="1"
												style="border: none; background: transparent; text-align: center; font-weight: bold; color: #17a2b8; width: 100%; font-size: 12px;"/>
										</div>
									</button>
								</div>
								<div style="text-align: center;">
									<button name="set_date_filter_30_60" type="object"
										class="btn btn-outline-warning aging-button"
										style="width: 120px; height: 120px; font-size: 11px; font-weight: bold; border-radius: 15px; border-width: 2px; box-shadow: 0 6px 12px rgba(255,193,7,0.25); transition: all 0.3s; background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%); position: relative; overflow: hidden; display: flex; flex-direction: column; justify-content: center; align-items: center;">
										<div style="font-size: 9px; color: #6c757d; margin-bottom: 2px; text-transform: uppercase; letter-spacing: 0.5px;">Recent</div>
										<div style="font-size: 13px; font-weight: bold; color: #ffc107; margin-bottom: 4px;">30-60 Days</div>
										<div style="font-size: 12px; font-weight: bold; color: #ffc107; background: rgba(255,193,7,0.1); padding: 2px 6px; border-radius: 4px;">
											<field name="thirty_sixty_days_filter" readonly="1"
												style="border: none; background: transparent; text-align: center; font-weight: bold; color: #ffc107; width: 100%; font-size: 12px;"/>
										</div>
									</button>
								</div>
								<div style="text-align: center;">
									<button name="set_date_filter_60_90" type="object"
										class="btn btn-outline-danger aging-button"
										style="width: 120px; height: 120px; font-size: 11px; font-weight: bold; border-radius: 15px; border-width: 2px; box-shadow: 0 6px 12px rgba(220,53,69,0.25); transition: all 0.3s; background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%); position: relative; overflow: hidden; display: flex; flex-direction: column; justify-content: center; align-items: center;">
										<div style="font-size: 9px; color: #6c757d; margin-bottom: 2px; text-transform: uppercase; letter-spacing: 0.5px;">Overdue</div>
										<div style="font-size: 13px; font-weight: bold; color: #dc3545; margin-bottom: 4px;">60-90 Days</div>
										<div style="font-size: 12px; font-weight: bold; color: #dc3545; background: rgba(220,53,69,0.1); padding: 2px 6px; border-radius: 4px;">
											<field name="sixty_ninty_days_filter" readonly="1"
												style="border: none; background: transparent; text-align: center; font-weight: bold; color: #dc3545; width: 100%; font-size: 12px;"/>
										</div>
									</button>
								</div>
								<div style="text-align: center;">
									<button name="set_date_filter_90_plus" type="object"
										class="btn btn-outline-dark aging-button"
										style="width: 120px; height: 120px; font-size: 11px; font-weight: bold; border-radius: 15px; border-width: 2px; box-shadow: 0 6px 12px rgba(52,58,64,0.25); transition: all 0.3s; background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%); position: relative; overflow: hidden; display: flex; flex-direction: column; justify-content: center; align-items: center;">
										<div style="font-size: 9px; color: #6c757d; margin-bottom: 2px; text-transform: uppercase; letter-spacing: 0.5px;">Critical</div>
										<div style="font-size: 13px; font-weight: bold; color: #343a40; margin-bottom: 4px;">90+ Days</div>
										<div style="font-size: 12px; font-weight: bold; color: #343a40; background: rgba(52,58,64,0.1); padding: 2px 6px; border-radius: 4px;">
											<field name="ninty_plus_days_filter" readonly="1"
												style="border: none; background: transparent; text-align: center; font-weight: bold; color: #343a40; width: 100%; font-size: 12px;"/>
										</div>
									</button>
								</div>
								<div style="text-align: center;">
									<button name="set_date_filter_total" type="object"
										class="btn btn-outline-success aging-button"
										style="width: 120px; height: 120px; font-size: 11px; font-weight: bold; border-radius: 15px; border-width: 2px; box-shadow: 0 6px 12px rgba(40,167,69,0.25); transition: all 0.3s; background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%); position: relative; overflow: hidden; display: flex; flex-direction: column; justify-content: center; align-items: center;">
										<div style="font-size: 9px; color: #6c757d; margin-bottom: 2px; text-transform: uppercase; letter-spacing: 0.5px;">All</div>
										<div style="font-size: 13px; font-weight: bold; color: #28a745; margin-bottom: 4px;">Total</div>
										<div style="font-size: 12px; font-weight: bold; color: #28a745; background: rgba(40,167,69,0.1); padding: 2px 6px; border-radius: 4px;">
											<field name="total_filter" readonly="1"
												style="border: none; background: transparent; text-align: center; font-weight: bold; color: #28a745; width: 100%; font-size: 12px;"/>
										</div>
									</button>
								</div>
								<!-- 左侧：日期和Get Statement按钮区域 -->
								<div style="display: flex; align-items: flex-start; gap: 15px;">
									<!-- 日期字段上下排列 -->
									<div style="display: flex; flex-direction: column; gap: 12px;">
										<div style="display: flex; align-items: center; gap: 15px;">
											<label for="statement_from_date" style="font-weight: bold; min-width: 80px;"/>
											<field name="statement_from_date" style="width: 130px; margin-bottom: 0px;"/>
										</div>
										<div style="display: flex; align-items: center; gap: 15px;">
											<div style="display: flex; align-items: center; gap: 15px;">
												<label for="statement_to_date" style="font-weight: bold; min-width: 80px;"/>
												<field name="statement_to_date" style="width: 130px; margin-bottom: 0px;"/>
											</div>
											<!-- Get Statement按钮 - 靠近to date -->
											<button name="do_process_statement_filter" class="btn btn-primary btn-sm" type="object" string="Get Statement" />
										</div>
									</div>
								</div>

								<!-- 右侧：Filter按钮组和Customer Statement按钮组 -->
								<div style="display: flex; align-items: flex-start; gap: 25px; margin-left: auto;">
									<!-- Filter按钮组 -->
									<div style="display: flex; flex-direction: column; gap: 8px;">
										<button name="do_print_statement_filter" class="btn btn-info btn-sm" type="object"
											string="Print Filter Statement" />
										<button name="do_send_statement_filter" class="btn btn-success btn-sm" type="object"
											string="Send Filter Statement" />
									</div>

									<!-- Customer Statement按钮组 -->
									<div style="display: flex; flex-direction: column; gap: 8px;">
										<button name="do_button_print_statement" class="btn btn-info btn-sm" type="object"
											string="Print Customer Statement" groups="account.group_account_manager"
											help="Print Customer Statement" invisible="statments in ('due','overdue')"/>
										<button name="customer_send_mail" class="btn btn-success btn-sm" type="object" string="Send Customer Statement"
											groups="account.group_account_manager"
											help="Print Customer report independent of follow-up line" invisible="statments in ('due','overdue')"/>
									</div>
								</div>
							</div>
						</div>

						<group invisible="1">
							<field name="first_thirty_day"/>
							<field name="thirty_sixty_days"/>
							<field name="sixty_ninty_days"/>
							<field name="ninty_plus_days"/>
							<field name="total"/>
						</group>
						<!-- 这些字段现在在按钮区域显示，保持隐藏状态以避免重复 -->
						<group invisible="1">
							<field name="first_thirty_day_filter"/>
							<field name="thirty_sixty_days_filter"/>
							<field name="sixty_ninty_days_filter"/>
							<field name="ninty_plus_days_filter"/>
							<field name="total_filter"/>
						</group>
						<group>
							<group>
								<field name="opening_balance" force_save="True" invisible="1"/>
							</group>
						</group>

						<field name="customer_statement_line_ids">
							<list string="Statement lines" editable="bottom" create="false"
								delete="false"
								colors="red:(not invoice_date_due or invoice_date_due&lt;=current_date) and result&gt;0;green:result==0 ">
								<field name="invoice_date" readonly="True" />
								<field name="name" readonly="True" />
								<field name="company_id" column_invisible="1"
									groups="base.group_multi_company" />
								<field name="invoice_date_due" readonly="True" />
								<field name="amount_total_signed" readonly="True" string="Invoices/Debits"
									sum="Total Debits " />
								<field name="credit_amount" readonly="True" string="Payments/Credits"
									sum="Total Credits" />
								<field name="result" readonly="True" string="Balance"
									sum="Total Balance" />
								<field name="amount_total" column_invisible="1" />
								<field name="amount_residual" column_invisible="1" />
								<field name="amount_residual_signed" column_invisible="1" />
							</list>
						</field>
						<group class="oe_subtotal_footer oe_right">
							<field name="filter_payment_amount_due_amt" />
						</group>
						<group class="oe_subtotal_footer oe_right">
							<field name="filter_payment_amount_overdue_amt" />
						</group>

						<separator string="Customer Statements"/>
						<!-- Due/Overdue按钮组 - 放在statement列表上面 -->
						<div style="display: flex; justify-content: center; gap: 15px; margin: 20px 0; padding: 15px; background: #f8f9fa; border-radius: 8px;">
							<button name="do_button_due_print" class="btn btn-info btn-sm" type="object"
								string="Print Due Payments" groups="account.group_account_manager"
								help="Print due payments report independent of follow-up line" invisible="statments == 'overdue'"/>
							<button name="do_due_partner_mail" class="btn btn-success btn-sm" type="object" string="Send Due Payment"
								groups="account.group_account_manager"
								help="If not specified by the latest follow-up level, it will send from the default email template" invisible="statments == 'overdue'"/>
							<button name="do_button_print" class="btn btn-info btn-sm" type="object"
								string="Print Overdue Payments" groups="account.group_account_manager"
								help="Print overdue payments report independent of follow-up line" invisible="statments == 'due'"/>
							<button name="do_partner_mail" class="btn btn-success btn-sm" type="object" string="Send Overdue Payment"
								groups="account.group_account_manager"
								help="If not specified by the latest follow-up level, it will send from the default email template" invisible="statments == 'due'" />
						</div>
						<field name="balance_invoice_ids">
							<list string="Invoice line" editable="bottom" create="false"
								delete="false"
								colors="red:(not invoice_date_due or invoice_date_due&lt;=current_date) and result&gt;0">
								<field name="invoice_date" readonly="True" />
								<field name="state" readonly="True" column_invisible="1" />
								<field name="name" readonly="True" />
								<field name="company_id" column_invisible="1"
									groups="base.group_multi_company" />
								<field name="invoice_date_due" readonly="True" />
								<field name="amount_total_signed" readonly="True" string="Invoices/Debits" />
								<field name="credit_amount" readonly="True" string="Payments/Credits" />
								<field name="result" readonly="True" string="Balance" />
								<field name="is_set_statments" column_invisible="1"></field>
							</list>
						</field>
						<group class="oe_subtotal_footer oe_right">
							<field name="payment_amount_due_amt" invisible="statments == 'overdue'" />
						</group>
						<group class="oe_subtotal_footer oe_right">
							<field name="payment_amount_overdue_amt" invisible="statments == 'due'" />
						</group>
					</page>

					<page string="Vendor Statements" groups="account.group_account_invoice"
						name="vendor_followup_tab"  context="{'res_partner_search_mode': 'supplier'}">
						<group></group>
						<br/>
						<separator string="Vendor Statements Filter By Date"/>

						<!-- 美化的供应商天数过滤器方块 -->
						<div style="margin: 20px 0;">
							<div style="display: flex; justify-content: center; gap: 20px; padding: 25px; background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); border-radius: 15px; box-shadow: 0 6px 20px rgba(0,0,0,0.1); border: 1px solid #dee2e6;">
								<div style="text-align: center;">
									<button name="set_vendor_date_filter_0_30" type="object"
										class="btn btn-outline-info aging-button"
										style="width: 120px; height: 120px; font-size: 11px; font-weight: bold; border-radius: 15px; border-width: 2px; box-shadow: 0 6px 12px rgba(23,162,184,0.25); transition: all 0.3s; background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%); position: relative; overflow: hidden; display: flex; flex-direction: column; justify-content: center; align-items: center;">
										<div style="font-size: 9px; color: #6c757d; margin-bottom: 2px; text-transform: uppercase; letter-spacing: 0.5px;">Current</div>
										<div style="font-size: 13px; font-weight: bold; color: #17a2b8; margin-bottom: 4px;">0-30 Days</div>
										<div style="font-size: 12px; font-weight: bold; color: #17a2b8; background: rgba(23,162,184,0.1); padding: 2px 6px; border-radius: 4px;">
											<field name="vendor_first_thirty_day_filter" readonly="1"
												style="border: none; background: transparent; text-align: center; font-weight: bold; color: #17a2b8; width: 100%; font-size: 12px;"/>
										</div>
									</button>
								</div>
								<div style="text-align: center;">
									<button name="set_vendor_date_filter_30_60" type="object"
										class="btn btn-outline-warning aging-button"
										style="width: 120px; height: 120px; font-size: 11px; font-weight: bold; border-radius: 15px; border-width: 2px; box-shadow: 0 6px 12px rgba(255,193,7,0.25); transition: all 0.3s; background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%); position: relative; overflow: hidden; display: flex; flex-direction: column; justify-content: center; align-items: center;">
										<div style="font-size: 9px; color: #6c757d; margin-bottom: 2px; text-transform: uppercase; letter-spacing: 0.5px;">Recent</div>
										<div style="font-size: 13px; font-weight: bold; color: #ffc107; margin-bottom: 4px;">30-60 Days</div>
										<div style="font-size: 12px; font-weight: bold; color: #ffc107; background: rgba(255,193,7,0.1); padding: 2px 6px; border-radius: 4px;">
											<field name="vendor_thirty_sixty_days_filter" readonly="1"
												style="border: none; background: transparent; text-align: center; font-weight: bold; color: #ffc107; width: 100%; font-size: 12px;"/>
										</div>
									</button>
								</div>
								<div style="text-align: center;">
									<button name="set_vendor_date_filter_60_90" type="object"
										class="btn btn-outline-danger aging-button"
										style="width: 120px; height: 120px; font-size: 11px; font-weight: bold; border-radius: 15px; border-width: 2px; box-shadow: 0 6px 12px rgba(220,53,69,0.25); transition: all 0.3s; background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%); position: relative; overflow: hidden; display: flex; flex-direction: column; justify-content: center; align-items: center;">
										<div style="font-size: 9px; color: #6c757d; margin-bottom: 2px; text-transform: uppercase; letter-spacing: 0.5px;">Overdue</div>
										<div style="font-size: 13px; font-weight: bold; color: #dc3545; margin-bottom: 4px;">60-90 Days</div>
										<div style="font-size: 12px; font-weight: bold; color: #dc3545; background: rgba(220,53,69,0.1); padding: 2px 6px; border-radius: 4px;">
											<field name="vendor_sixty_ninty_days_filter" readonly="1"
												style="border: none; background: transparent; text-align: center; font-weight: bold; color: #dc3545; width: 100%; font-size: 12px;"/>
										</div>
									</button>
								</div>
								<div style="text-align: center;">
									<button name="set_vendor_date_filter_90_plus" type="object"
										class="btn btn-outline-dark aging-button"
										style="width: 120px; height: 120px; font-size: 11px; font-weight: bold; border-radius: 15px; border-width: 2px; box-shadow: 0 6px 12px rgba(52,58,64,0.25); transition: all 0.3s; background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%); position: relative; overflow: hidden; display: flex; flex-direction: column; justify-content: center; align-items: center;">
										<div style="font-size: 9px; color: #6c757d; margin-bottom: 2px; text-transform: uppercase; letter-spacing: 0.5px;">Critical</div>
										<div style="font-size: 13px; font-weight: bold; color: #343a40; margin-bottom: 4px;">90+ Days</div>
										<div style="font-size: 12px; font-weight: bold; color: #343a40; background: rgba(52,58,64,0.1); padding: 2px 6px; border-radius: 4px;">
											<field name="vendor_ninty_plus_days_filter" readonly="1"
												style="border: none; background: transparent; text-align: center; font-weight: bold; color: #343a40; width: 100%; font-size: 12px;"/>
										</div>
									</button>
								</div>
								<div style="text-align: center;">
									<button name="set_vendor_date_filter_total" type="object"
										class="btn btn-outline-success aging-button"
										style="width: 120px; height: 120px; font-size: 11px; font-weight: bold; border-radius: 15px; border-width: 2px; box-shadow: 0 6px 12px rgba(40,167,69,0.25); transition: all 0.3s; background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%); position: relative; overflow: hidden; display: flex; flex-direction: column; justify-content: center; align-items: center;">
										<div style="font-size: 9px; color: #6c757d; margin-bottom: 2px; text-transform: uppercase; letter-spacing: 0.5px;">All</div>
										<div style="font-size: 13px; font-weight: bold; color: #28a745; margin-bottom: 4px;">Total</div>
										<div style="font-size: 12px; font-weight: bold; color: #28a745; background: rgba(40,167,69,0.1); padding: 2px 6px; border-radius: 4px;">
											<field name="vendor_total_filter" readonly="1"
												style="border: none; background: transparent; text-align: center; font-weight: bold; color: #28a745; width: 100%; font-size: 12px;"/>
										</div>
									</button>
								</div>

								<!-- 左侧：日期和Get Statement按钮区域 -->
								<div style="display: flex; align-items: flex-start; gap: 15px;">
									<!-- 日期字段上下排列 -->
									<div style="display: flex; flex-direction: column; gap: 12px;">
										<div style="display: flex; align-items: center; gap: 15px;">
											<label for="vendor_statement_from_date" style="font-weight: bold; min-width: 80px;"/>
											<field name="vendor_statement_from_date" style="width: 130px;"/>
										</div>
										<div style="display: flex; align-items: center; gap: 15px;">
											<div style="display: flex; align-items: center; gap: 15px;">
												<label for="vendor_statement_to_date" style="font-weight: bold; min-width: 80px;"/>
												<field name="vendor_statement_to_date" style="width: 130px;"/>
											</div>
											<!-- Get Statement按钮 - 靠近to date -->
											<button name="do_process_vendor_statement_filter" class="btn btn-primary btn-sm" type="object" string="Get Statement" />
										</div>
									</div>
								</div>

								<!-- 右侧：Filter按钮组和Vendor Statement按钮组 -->
								<div style="display: flex; align-items: flex-start; gap: 25px; margin-left: auto;">
									<!-- Filter按钮组 -->
									<div style="display: flex; flex-direction: column; gap: 8px;">
										<button name="do_print_vendor_statement_filter" class="btn btn-info btn-sm" type="object"
											string="Print Filter Statement" />
										<button name="supplier_send_mail" class="btn btn-success btn-sm" type="object"
											string="Send Filter Statement" />
									</div>

									<!-- Vendor Statement按钮组 -->
									<div style="display: flex; flex-direction: column; gap: 8px;">
										<button name="do_button_print_vendor_statement" class="btn btn-info btn-sm" type="object" string="Print Vendor Statement" groups="account.group_account_manager" help="Print Vendor Statement" invisible="statments in ('due','overdue')"/>
										<button name="supplier_send_mail" class="btn btn-success btn-sm" type="object" string="Send Vendor Statement" groups="account.group_account_manager" help="Send Vendor Statement" invisible="statments in ('due','overdue')"/>
									</div>
								</div>
							</div>
						</div>



						<group>
							<group>
								<field name="vendor_opening_balance" force_save="True" invisible="1"/>
							</group>
						</group>

						<!-- 供应商filter字段，保持隐藏状态以避免重复 -->
						<group invisible="1">
							<field name="vendor_first_thirty_day_filter"/>
							<field name="vendor_thirty_sixty_days_filter"/>
							<field name="vendor_sixty_ninty_days_filter"/>
							<field name="vendor_ninty_plus_days_filter"/>
							<field name="vendor_total_filter"/>
						</group>

						<field name="vendor_statement_line_ids">
							<list string="Statement lines" editable="bottom" create="false"
								delete="false"
								colors="red:(not invoice_date_due or invoice_date_due&lt;=current_date) and result&gt;0;green:result==0 ">
								<field name="invoice_date" readonly="True" />
								<field name="name" readonly="True" />
								<field name="company_id" column_invisible="1"
									groups="base.group_multi_company" />
								<field name="invoice_date_due" readonly="True" />
								<field name="amount_total_signed" readonly="True" string="Invoices/Debits"
									sum="Total Debits " />
								<field name="credit_amount" readonly="True" string="Payments/Credits"
									sum="Total Credits" />
								<field name="result" readonly="True" string="Balance"
									sum="Total Balance" />
								<field name="amount_total" column_invisible="1" />
								<field name="amount_residual" column_invisible="1" />
								<field name="amount_residual_signed" column_invisible="1" />
							</list>
						</field>
						<group class="oe_subtotal_footer oe_right">
							<field name="filter_payment_amount_due_amt_supplier" />
							<field name="filter_payment_amount_overdue_amt_supplier" />
						</group>
						<group></group>
						<br/>
						<separator string="Vendor Statements"/>
						<!-- Vendor Due/Overdue按钮组 - 放在statement列表上面 -->
						<div style="display: flex; justify-content: center; gap: 15px; margin: 20px 0; padding: 15px; background: #f8f9fa; border-radius: 8px;">
							<button name="do_button_supplier_due_print" class="btn btn-info btn-sm" type="object"
								string="Print Due Payments" groups="account.group_account_manager"
								help="Print overdue payments report independent of follow-up line" invisible="statments == 'overdue'"/>
							<button name="do_due_supplier_partner_mail" class="btn btn-success btn-sm" type="object" string="Send Due Payment"
								groups="account.group_account_manager"
								help="If not specified by the latest follow-up level, it will send from the default email template" invisible="statments == 'overdue'"/>
							<button name="do_supplier_button_print" class="btn btn-info btn-sm" type="object"
								string="Print Overdue Payments" groups="account.group_account_manager"
								help="Print overdue payments report independent of follow-up line" invisible="statments == 'due'" />
							<button name="supplier_do_partner_mail" class="btn btn-success btn-sm" type="object" string="Send Overdue Payment"
								groups="account.group_account_manager"
								help="If not specified by the latest follow-up level, it will send from the default email template" invisible="statments == 'due'"/>
						</div>
						<field name="supplier_invoice_ids">
							<list string="Supplier Invoice line" editable="bottom"
								create="false" delete="false"
								colors="red:(not invoice_date_due or invoice_date_due&lt;=current_date) and result&gt;0">
								<field name="invoice_date" readonly="True" />
								<field name="state" readonly="True" column_invisible="1" />
								<field name="name" readonly="True" />
								<field name="company_id" column_invisible="1"
									groups="base.group_multi_company" />
								<field name="invoice_date_due" readonly="True" />
								<field name="amount_total_signed" readonly="True" string="Invoices/Debits" />
								<field name="credit_amount" readonly="True" string="Payments/Credits" />
								<field name="result" readonly="True" string="Balance" />
							</list>
						</field>
						<group class="oe_subtotal_footer oe_right">
							<field name="payment_amount_due_amt_supplier" invisible="statments == 'overdue'"/>
							<field name="payment_amount_overdue_amt_supplier" invisible="statments == 'due'"/>
						</group>
					</page>
				</xpath>
<!--				</page>-->
			</field>
		</record>

		<record id="view_partner_inherit_followup_search" model="ir.ui.view">
			<field name="name">res.partner.search.inherited</field>
			<field name="model">res.partner</field>
			<field name="inherit_id" ref="base.view_res_partner_filter"/>
			<field name="arch" type="xml">
				<xpath expr="//filter[@name='supplier']" position="after">
					<filter string="Overdue Customers" name="customer_overdue" domain="[('payment_amount_overdue_amt','>',0)]" context="{'res_partner_search_mode': 'customer'}"/>
				</xpath>
			</field>
		</record>



        <record id="id_wizard_window_action" model="ir.actions.act_window">
            <field name="name">Send Overdue Payment</field>
            <field name="res_model">send.overdue.statement</field>
            <field name="view_mode">form</field>
            <field name="view_id" ref="view_send_overdue_statement"/>
            <field name="binding_model_id" ref="base.model_res_partner"/>
            <field name="target">new</field>
            <field name="domain">[('id','in',active_ids)]</field>

        </record>

	</data>
</odoo>
