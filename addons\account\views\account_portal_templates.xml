<odoo>
    <template id="portal_my_home_menu_invoice" name="Portal layout : invoice menu entries" inherit_id="portal.portal_breadcrumbs" priority="30">
        <xpath expr="//ol[hasclass('o_portal_submenu')]" position="inside">
            <li t-if="page_name == 'invoice'" t-attf-class="breadcrumb-item #{'active ' if not invoice else ''}">
                <a t-if="invoice" t-attf-href="/my/invoices?{{ keep_query() }}">Invoices &amp; Bills</a>
                <t t-else="">Invoices &amp; Bills</t>
            </li>
            <li t-if="invoice" class="breadcrumb-item active">
                <t t-out="invoice.name" t-if="invoice.name != '/'"/>
                <t t-else=""><em>Draft Invoice</em></t>
            </li>
        </xpath>
    </template>

    <template id="portal_my_home_invoice" name="Invoices / Bills" inherit_id="portal.portal_my_home" customize_show="True" priority="30">
        <xpath expr="//div[hasclass('o_portal_docs')]" position="before">
            <t t-set="portal_client_category_enable" t-value="True"/>
            <t t-set="portal_vendor_category_enable" t-value="True"/>
        </xpath>
        <div id="portal_client_category" position="inside">
            <t t-call="portal.portal_docs_entry">
                <t t-set="icon" t-value="'/account/static/src/img/Bill.svg'"/>
                <t t-set="title">Your Invoices</t>
                <t t-set="url" t-value="'/my/invoices?filterby=invoices'"/>
                <t t-set="text">Follow, download or pay your invoices</t>
                <t t-set="placeholder_count" t-value="'invoice_count'"/>
            </t>
        </div>
        <div id="portal_vendor_category" position="inside">
            <t t-call="portal.portal_docs_entry">
                <t t-set="icon" t-value="'/account/static/src/img/Bill.svg'"/>
                <t t-set="title">Our Invoices</t>
                <t t-set="url" t-value="'/my/invoices?filterby=bills'"/>
                <t t-set="text">Follow, download or pay our invoices</t>
                <t t-set="placeholder_count" t-value="'bill_count'"/>
            </t>
        </div>
    </template>

    <template id="portal_my_invoices" name="My Invoices and Payments">
      <t t-call="portal.portal_layout">
        <t t-set="breadcrumbs_searchbar" t-value="True"/>

        <t t-call="portal.portal_searchbar">
            <t t-set="title">Invoices</t>
        </t>
        <t t-if="not invoices">
            <p class="alert alert-warning">There are currently no invoices and payments for your account.</p>
        </t>
        <t t-if="invoices" t-call="portal.portal_table">
            <thead>
                <tr class="active">
                    <th name="invoice_number">Invoice #</th>
                    <th name="invoice_date">Invoice Date</th>
                    <th name="due_date" class='d-none d-md-table-cell'>Due Date</th>
                    <th name="amount_due" class="text-end pe-3">Amount Due</th>
                    <th name="status">Status</th>
                </tr>
            </thead>
            <tbody>
                <t t-foreach="invoices" t-as="invoice_data">
                    <t t-set="invoice" t-value="invoice_data['invoice']"/>
                    <tr>
                        <td>
                            <a t-att-href="invoice.get_portal_url()" t-att-title="invoice.name">
                                <t t-out="invoice.name" t-if="invoice.name != '/'"/>
                                <em t-else="">Draft Invoice</em>
                            </a>
                        </td>
                        <td><span t-field="invoice.invoice_date"/></td>
                        <td class='d-none d-md-table-cell'
                            t-att-class="'text-danger' if invoice.invoice_date_due and invoice.invoice_date_due &lt; datetime.date.today() and invoice.payment_state in ['not_paid', 'partial'] else ''">
                            <span t-field="invoice.invoice_date_due"/>
                        </td>
                        <td class="text-end pe-3"><span t-out="-invoice.amount_residual if invoice.move_type == 'out_refund' else invoice.amount_residual" t-options='{"widget": "monetary", "display_currency": invoice.currency_id}'/></td>
                        <td name="invoice_status">
                            <t t-if="invoice.state == 'posted'" name="invoice_status_posted">
                                <span t-if="invoice.currency_id.is_zero(invoice.amount_residual)"
                                      class="badge rounded-pill text-bg-success">
                                    <i class="fa fa-fw fa-check" aria-label="Paid" title="Paid" role="img"/>
                                    <span class="d-none d-md-inline"> Paid</span>
                                </span>
                                <span t-elif="invoice.payment_state == 'in_payment' and not invoice.currency_id.is_zero(invoice.amount_residual)"
                                      class="badge rounded-pill text-bg-info">
                                    <i class="fa fa-fw fa-check" aria-label="processing_payment" title="Processing Payment" role="img"/>
                                    <span class="d-none d-md-inline"> Processing Payment</span>
                                </span>
                                <span t-elif="invoice.payment_state == 'reversed'"
                                      class="badge rounded-pill text-bg-success">
                                    <i class="fa fa-fw fa-check" aria-label="Reversed" title="Reversed" role="img"/>
                                    <span class="d-none d-md-inline"> Reversed</span>
                                </span>
                                <span t-else="" class="badge rounded-pill text-bg-info" name="invoice_status_waiting_for_payment">
                                    <i class="fa fa-fw fa-clock-o" aria-label="Opened" title="Opened" role="img"/>
                                    <span class="d-none d-md-inline"> Waiting for Payment</span>
                                </span>
                            </t>
                            <t t-elif="invoice.state == 'cancel'">
                                <span class="badge rounded-pill text-bg-warning">
                                    <i class="fa fa-fw fa-remove" aria-label="Cancelled" title="Cancelled" role="img"/>
                                    <span class="d-none d-md-inline"> Cancelled</span>
                                </span>
                            </t>
                        </td>
                    </tr>
                </t>
            </tbody>
        </t>
      </t>
    </template>

    <template id="portal_invoice_page" name="Invoice/Bill" inherit_id="portal.portal_sidebar" primary="True">
        <xpath expr="//div[hasclass('o_portal_sidebar')]" position="inside">
            <t t-set="o_portal_fullwidth_alert" groups="sales_team.group_sale_salesman,account.group_account_invoice,account.group_account_readonly">
                <t t-call="portal.portal_back_in_edit_mode">
                    <t t-set="backend_url" t-value="'/odoo/action-account.action_move_out_invoice_type/%s' % invoice.id"/>
                </t>
            </t>

            <div class="row o_portal_invoice_sidebar">
                <!-- Sidebar -->
                <t t-call="portal.portal_record_sidebar">
                    <t t-set="classes" t-value="'col-lg-4 col-xxl-3 d-print-none'"/>
                    <t t-set="title">
                        <h2 class="mb-0 text-break mx-auto">
                            <span t-field="invoice.amount_total"/>
                        </h2>
                        <div class="my-1 w-100" t-if="payment_state in ('not_paid', 'partial')">
                            <div class="alert alert-success px-2 py-1 text-center mb-2" t-if="payment_state == 'partial'">
                                Already Paid: <span t-out="amount_paid" t-options="{'widget': 'monetary', 'display_currency': currency}"/>
                            </div>
                            <div t-if="payment_state == 'partial'" class="alert alert-warning px-2 py-1 text-center mb-2">
                                Left to Pay:
                                <span t-out="amount_due" t-options="{'widget': 'monetary', 'display_currency': currency}"/>
                                <div t-if="installment_state and is_last_installment" class="w-100 mt-2">
                                    <i class="fa fa-clock-o"/>
                                    <span class="o_portal_sidebar_timeago" t-att-datetime="due_date"/>
                                </div>
                            </div>
                            <div t-if="installment_state == 'epd' and epd_discount_amount_currency" class="alert alert-warning px-2 py-1 text-center mb-2">
                                <t t-out="epd_discount_msg"/>
                            </div>
                        </div>
                        <t t-if="installment_state in ('next', 'overdue') and not is_last_installment and amount_due != 0.0 and payment_state != 'in_payment'">
                            <h5 name="installment_title" class="w-100 text-center mb-0">
                                <t t-if="installment_state == 'next'">Next Installment</t>
                                <t t-elif="installment_state == 'overdue'">Overdue</t>
                            </h5>
                            <h4
                                name="installment_amount"
                                class="mb-0 text-break w-100 text-center"
                                t-out="next_amount_to_pay"
                                t-options="{'widget': 'monetary', 'display_currency': currency}"
                            />
                        </t>
                        <div class="small w-100 text-center" t-if="payment_state in ('not_paid', 'partial') and not (is_last_installment and installment_state)">
                            <i class="fa fa-clock-o"/>
                            <span class="o_portal_sidebar_timeago ml4" t-att-datetime="next_due_date"/>
                        </div>
                    </t>

                    <t t-set="entries">
                        <div class="d-flex flex-column gap-4 mt-3">
                            <div class="d-flex flex-column gap-2">
                                <div class="o_download_pdf d-flex flex-lg-column flex-xl-row flex-wrap gap-2">
                                    <a class="btn btn-light o_download_btn flex-grow-1" t-att-href="invoice.get_portal_url(report_type='pdf', download=True)" title="Download" role="button">
                                        <i class="fa fa-download"/> Download
                                    </a>
                                </div>
                            </div>
                            <div t-if="invoice.invoice_user_id" class="flex-grow-1">
                                <h6>
                                    <small class="text-muted">
                                        <t t-if="invoice.move_type == 'out_invoice'">
                                            Salesperson
                                        </t>
                                        <t t-if="invoice.move_type == 'in_invoice'">
                                            Purchase Representative
                                        </t>
                                    </small>
                                </h6>
                                <t t-call="portal.portal_my_contact">
                                    <t t-set="_contactAvatar" t-value="image_data_uri(invoice.invoice_user_id.avatar_128)"/>
                                    <t t-set="_contactName" t-value="invoice.invoice_user_id.name"/>
                                    <t t-set="_contactLink" t-value="True"/>
                                    <div t-field="invoice.invoice_user_id" t-options='{"widget": "contact", "fields": ["city", "phone"]}'/>
                                </t>
                            </div>
                        </div>
                    </t>
                </t>

                <!-- Page Content -->
                <div id="invoice_content" class="o_portal_content col-12 col-lg-8 col-xxl-9">
                    <t t-if="error or warning" t-call="account.portal_invoice_error"/>
                    <t t-if="success and (not error and not warning)" t-call="account.portal_invoice_success"/>

                    <div class="o_portal_html_view position-relative bg-white shadow overflow-hidden">
                        <div class="o_portal_html_loader text-center">
                            <i class="fa fa-circle-o-notch fa-spin fa-2x fa-fw text-black-50"></i>
                        </div>
                        <iframe id="invoice_html" class="position-relative d-block" width="100%" height="100%" frameborder="0" scrolling="no" t-att-src="invoice.get_portal_url(report_type='html')"/>
                    </div>
                    <!-- chatter -->
                    <div id="invoice_communication" class="mt-4">
                        <h3>Communication history</h3>
                        <t t-call="portal.message_thread"/>
                    </div>
                </div>
            </div>
        </xpath>
    </template>

    <template id="portal_invoice_error" name="Invoice error/warning display">
        <div class="row mr16">
            <div t-attf-class="'col-lg-12 mr16 ml16 alert alert-dismissable' #{'alert-danger' if error else 'alert-warning'}" role="alert">
                <a href="#" class="close" data-bs-dismiss="alert" aria-label="close" title="close">×</a>
                <t t-if="error == 'generic'" name="generic">
                    There was an error processing this page.
                </t>
            </div>
        </div>
    </template>

    <template id="portal_invoice_success" name="Invoice success display">
        <div class="row mr16">
            <div class="col-lg-12 mr16 ml16 alert alert-dismissable alert-success" role="status">
                <a href="#" class="close" data-bs-dismiss="alert" aria-label="close" title="close">×</a>
            </div>
        </div>
    </template>

    <!-- Get the fields set in required_fields and display them as form elements. Doesn't create the form itself. -->
    <template id="portal_invoice_required_fields_form">
        <t t-foreach="required_fields" t-as="required_field">
            <div t-attf-class="mb-3 #{error.get(required_field.name) and 'o_has_error' or ''} col-xl-6">
                <!-- select by default the value passed in the data or corresponding to the "default" attribute on the field -->
                <t t-set="field_info" t-value="env[required_field.model]._fields[required_field.name]"/>
                <t t-set="default_value" t-value="extra_field_values.get(field_prefix + required_field.name) or field_info.default and field_info.default(required_field.model)"/>
                <t t-if="required_field.ttype == 'selection'">
                    <label class="col-form-label" t-att-for="field_prefix + required_field.name"><t t-out="required_field.field_description"/></label>
                    <select class="form-select" t-att-name="field_prefix + required_field.name" required="required">
                        <option t-att-selected="not default_value" disabled="disabled" value=""><t t-out="'Select the %s ...' % required_field.field_description.lower()"/></option>
                        <t t-foreach="required_field.selection_ids" t-as="selection">
                            <option t-att-selected="default_value and default_value == selection.value" t-att-value="selection.value"><t t-out="selection.name"/></option>
                        </t>
                    </select>
                </t>
            </div>
        </t>
    </template>

    <template id="portal_my_details_fields" inherit_id="portal.portal_my_details_fields">
        <xpath expr="//select[@name='country_id']/.." position="after">
            <div class="col-12 d-none d-xl-block">
                <small class="form-text text-muted">
                    You can choose how you want us to send your invoices, and with which electronic format.
                </small>
            </div>
            <div class="row m-0 p-0" t-if="len(invoice_sending_methods) > 1">
                <div class="col-xl-6">
                    <label class="col-form-label" for="invoice_sending_method">Receive invoices</label>
                    <select name="invoice_sending_method" class="form-select">
                        <t t-foreach="invoice_sending_methods" t-as="method">
                            <option t-att-value="method" t-att-selected="(invoice_sending_method or partner.invoice_sending_method) == method">
                                <t t-esc="method_value"/>
                            </option>
                        </t>
                    </select>
                </div>
            </div>
            <t t-if="invoice_edi_formats">
                <div class="row m-0 mb-3 p-0">
                    <div class="col-xl-6">
                        <label class="col-form-label" for="invoice_edi_format">Electronic format</label>
                        <select name="invoice_edi_format" class="form-select">
                            <option value=""/>
                            <t t-foreach="invoice_edi_formats" t-as="format">
                                <option t-att-value="format" t-att-selected="(invoice_edi_format or partner.invoice_edi_format) == format">
                                    <t t-esc="format_value"/>
                                </option>
                            </t>
                        </select>
                    </div>
                </div>
            </t>
        </xpath>
    </template>
</odoo>
