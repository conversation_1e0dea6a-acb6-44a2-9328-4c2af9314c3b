<templates>

    <t t-name="account.DocumentFileUploader">
        <FileUploader
            acceptedFileExtensions="props.acceptedFileExtensions"
            fileUploadClass="'document_file_uploader'"
            multiUpload="true"
            onUploaded.bind="onFileUploaded"
            onUploadComplete.bind="onUploadComplete">
            <t t-set-slot="toggler">
                <t t-slot="toggler"/>
            </t>
            <t t-slot="default"/>
        </FileUploader>
    </t>

    <t t-name="account.DocumentViewUploadButton">
        <DocumentFileUploader resModel="props.resModel">
            <t t-set-slot="toggler">
                <button type="button" class="btn btn-secondary" data-hotkey="shift+i">
                    Upload
                </button>
            </t>
        </DocumentFileUploader>
    </t>

</templates>
