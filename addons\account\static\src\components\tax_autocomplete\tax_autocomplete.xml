<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">
    <t t-name="account.TaxAutoComplete" t-inherit="web.AutoComplete">
        <xpath expr="//t[@t-esc='option.label']" position="replace">
            <t t-if="option.tax_scope">
                <div class="tax_autocomplete_grid">
                    <div t-esc="option.label"/>
                    <div t-esc="option.tax_scope" class="text-muted"/>
                </div>
            </t>
            <t t-else="">
                <span t-esc="option.label"/>
            </t>
        </xpath>
    </t>
</templates>
