<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="view_account_secure_entries_wizard" model="ir.ui.view">
            <field name="name">account.secure.entries.wizard.form</field>
            <field name="model">account.secure.entries.wizard</field>
            <field name="arch" type="xml">
                <form string="Secure Journal Entries">
                    <sheet>
                        <div class="m-0" name="warnings" invisible="not warnings">
                            <field name="warnings" class="o_field_html" widget="actionable_errors"/>
                        </div>
                        <field name="company_id" invisible="1"/>
                        <div class="d-flex" name="hash_date">
                            <span class="text-muted">
                                <i>Secure entries up to</i>
                            </span>
                            <field name="hash_date" placeholder="Not hashed"
                                   class="oe_inline  ms-2"
                                   options="{'warn_future': true}"/>
                            <span class="text-muted ms-2">
                                <i>inclusive, to make them immutable</i>
                            </span>
                        </div>
                    </sheet>
                    <footer>
                        <button string="Secure Entries" class="btn-primary" name="action_secure_entries" type="object" data-hotkey="v"/>
                        <button string="Discard" special="cancel" data-hotkey="z"/>
                    </footer>
                </form>
            </field>
        </record>

        <record id="action_view_account_secure_entries_wizard" model="ir.actions.act_window">
            <field name="name">Secure Journal Entries</field>
            <field name="res_model">account.secure.entries.wizard</field>
            <field name="view_mode">form</field>
            <field name="view_id" ref="view_account_secure_entries_wizard"/>
            <field name="target">new</field>
        </record>

        <menuitem
            id="menu_action_secure_entries"
            name="Secure Entries"
            action="action_view_account_secure_entries_wizard"
            parent="account.menu_finance_entries"
            sequence="80"
            groups="base.group_no_one,account.group_account_secured"/>
    </data>
</odoo>
