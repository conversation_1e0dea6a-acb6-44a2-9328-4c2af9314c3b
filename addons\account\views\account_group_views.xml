<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <record id="view_account_group_form" model="ir.ui.view">
            <field name="name">account.group.form</field>
            <field name="model">account.group</field>
            <field name="arch" type="xml">
                <form string="Account Group">
                <sheet>
                    <group>
                        <field name="name"/>
                        <label for="code_prefix_start" string="Code Prefix"/>
                        <div>
                            From <field name="code_prefix_start" class="oe_inline"/> to <field name="code_prefix_end" class="oe_inline"/>
                        </div>
                        <field name="company_id" options="{'no_create': True}" groups="base.group_multi_company"/>
                    </group>
                </sheet>
                </form>
            </field>
        </record>

        <record id="view_account_group_search" model="ir.ui.view">
            <field name="name">account.group.search</field>
            <field name="model">account.group</field>
            <field name="arch" type="xml">
                <search string="Account groups">
                    <field name="name"
                           filter_domain="['|', ('code_prefix_start', '=like', self + '%'), ('name', 'ilike', self)]"
                           string="Account group"/>
                </search>
            </field>
        </record>

        <record id="view_account_group_tree" model="ir.ui.view">
            <field name="name">account.group.list</field>
            <field name="model">account.group</field>
            <field name="arch" type="xml">
                <list string="Account Group">
                    <field name="code_prefix_start"/>
                    <field name="code_prefix_end"/>
                    <field name="name"/>
                    <field name="company_id" groups="base.group_multi_company"/>
                </list>
            </field>
        </record>

    </data>
</odoo>
